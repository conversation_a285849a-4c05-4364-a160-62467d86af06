import React from 'react';
import { Client, GraphError } from '@microsoft/microsoft-graph-client';
import { IChatReaction, IChatResponse } from '../../types/IChatResponse';
import { WeakTokenProvider } from '../../types/TokenProvider';
import {
  IBatchResponseStatus,
  IRequestPayload,
  UseGraphApiError,
  initGraphClient,
  sendRequests,
} from './useGraphApiAccessor';

export interface IChatDetailBase {
  displayName: string;
}

export interface IChatDetail extends IChatDetailBase {
  topic: string | null;
  members: {
    userId: string;
    displayName: string;
    email: string;
  }[];
}

export interface IChannelDetail extends IChatDetailBase {
  subject: string | null;
}

export type UseChatApiReturnType = {
  fetchChatDetail: FetchChatDetail | undefined,
  fetchChatRepliesInBackground: FetchChatRepliesInBackground | undefined,
  fetchChatAttachment: FetchChatAttachment | undefined,
  fetchChatChannelName: FetchChatChannelName | undefined,
  fetchChatTeamName: FetchChatTeamName | undefined,
  fetchChatThreadTitle: FetchChatThreadTitle | undefined,
  fetchChatMember: FetchChatMember | undefined,
  fetchReactionUserNames: FetchReactionUserNames | undefined,
}

export type FetchChatRepliesInBackground = (
  teamId: string,
  chatId: string,
  messageId: string,
  messageType: string,
) => Promise<IChatResponse | null>;

export type FetchChatDetail = (
  chatId: string,
  messageId: string,
) => Promise<IChatResponse>

export type FetchChatAttachment = (srcUrl: string) => Promise<Blob>;

export type FetchChatChannelName = (
  teamId: string,
) => Promise<IChatDetailBase>

export type FetchChatTeamName = (
  teamId: string,
  messageId: string,
) => Promise<IChatDetailBase>

export type FetchChatThreadTitle = (
  teamId: string,
  chatId: string,
  messageId: string,
) => Promise<IChannelDetail>

export type FetchChatMember = (
  chatId: string,
) => Promise<Pick<IChatResponse & IChatDetail, 'topic' | 'members'>>

export type FetchReactionUserNames = (
  reactions: IChatReaction[],
) => Promise<IBatchResponseStatus<{
  displayName: string;
  id: string;
}>>

export async function fetchBulkReactionUserNames(
  tokenProvider: WeakTokenProvider,
  cancellationRef: React.MutableRefObject<boolean>,
  reactions: IChatReaction[],
) {
  // 1つのリクエストに含めるユーザーの数
  const BatchUserSize = 40;

  if (reactions.length === 0) {
    return Promise.resolve({
      errors: [],
      responses: [],
      recoverable: [],
      totalTooManyRequests: 0,
      tooManyRequests: [],
    });
  }

  // BatchUserSize毎にリクエストを分ける
  let oids: string[] = [];
  const transformed: string[][] = [];
  reactions.forEach((reaction, index) => {
    oids.push(`'${reaction.user.user.id}'`);
    if ((index + 1) % BatchUserSize === 0 || index === reactions.length - 1) {
      transformed.push(oids);
      oids = [];
    }
  });
  const requests = transformed.map((ids, index) => ({
    url: `users?$filter=id in (${ids.join(',')})&$select=displayName,id`,
    id: `${index}`,
    method: 'GET',
  } as IRequestPayload));

  const result = await sendRequests<{
    value: [{ displayName: string, id: string }]
  }>(tokenProvider, requests, cancellationRef);
  const converted = {
    ...result,
    responses: result.responses.flatMap((v) => v.value),
  };
  return converted;
}
/**
 * 指定されたメッセージIDに一致するメッセージを取得するための再帰関数
 * @param client - APIクライアント
 * @param nextLink - 次のページのリンク
 * @param messageId - 検索対象のメッセージID
 * @returns 指定されたメッセージIDに一致するメッセージ、またはnull
 * @returns
 */
async function fetchMessages(
  client: Client,
  nextLink: string,
  messageId: string,
): Promise<IChatResponse | null> {
  // 次のページのメッセージを取得
  const chatRes = await client.api(nextLink).get();
  // ルートメッセージの中から指定されたメッセージIDに一致するメッセージを検索
  const rootMessage = chatRes.value.find(
    (parentId: { id: string; }) => parentId.id === messageId,
  );
  if (rootMessage) {
    const updatedRootMessage = {
      ...rootMessage,
      teamChatType: 'root',
    };
    return updatedRootMessage;
  }

  const messageInReplies = chatRes.value
    .flatMap((parentMessageReplies:
      { replies?: { id: string } }) => parentMessageReplies.replies || [])
    .find((reply: { id: string; }) => reply.id === messageId);
  if (messageInReplies) {
    const updatedReplyMessage = {
      ...messageInReplies,
      teamChatType: 'reply',
    };
    return updatedReplyMessage;
  }
  // 次のページが存在する場合、再帰的に次のページを検索
  if (chatRes['@odata.nextLink']) {
    return fetchMessages(client, chatRes['@odata.nextLink'], messageId);
  }
  return null;
}
/**
 * 指定されたメッセージIDに一致するメッセージをバックグラウンドで取得する関数。
 *
 * @param tokenProvider - トークンプロバイダー
 * @param chatId - チャットID
 * @param messageId -activeItem.id
 * @param teamId - チームID
 * @param messageType - メッセージの種類（'team'またはその他）
 * @returns 指定されたメッセージIDに一致するメッセージ、またはnull
 */
async function fetchChatRepliesInBackgroundImpl(
  tokenProvider: WeakTokenProvider,
  chatId: string,
  messageId: string,
  teamId: string,
  messageType: string,
): Promise<IChatResponse | null> {
  const client = initGraphClient(tokenProvider);
  if (!client) return Promise.reject(new Error(UseGraphApiError.TOKEN_PROVIDER_NOT_AVAILABLE));

  if (messageType === 'team') {
    // 初期リンクを構築してメッセージとその返信を取得
    const initialLink = `teams/${teamId}/channels/${chatId}/messages?$top=50&$expand=replies`;
    const result = await fetchMessages(client, initialLink, messageId);
    if (result === null) {
      return Promise.reject(new Error('Message not found.'));
    }
    return result;
  }
  // messageTypeが'team'以外であれば何もしない
  return null;
}

/**
 * @return reject時にはErrorまたはGraphErrorを返却する
 */
async function fetchChatDetailImpl(
  tokenProvider: WeakTokenProvider, chatId: string, messageId: string,
): Promise<IChatResponse> {
  const client = initGraphClient(tokenProvider);
  if (!client) return Promise.reject(new Error(UseGraphApiError.TOKEN_PROVIDER_NOT_AVAILABLE));
  const res = await client
    .api(`me/chats/${chatId}/messages/${messageId}`)
    .get();
  // Indexの中にあるがAPI側には存在しないケースのエラーハンドリング
  if (res.deletedDateTime) {
    const error = new GraphError(403);
    error.message = 'Message has been deleted';
    return Promise.reject(error);
  }
  return res;
}

async function fetchChatChannelNameImpl(
  tokenProvider: WeakTokenProvider, teamId: string,
): Promise<IChatDetailBase> {
  const client = initGraphClient(tokenProvider);
  if (!client) return Promise.reject(new Error(UseGraphApiError.TOKEN_PROVIDER_NOT_AVAILABLE));

  const res = await client
    .api(`teams/${teamId}`)
    .get();
  return res;
}

/**
 * @return reject時にはErrorまたはGraphErrorを返却する
 */
function fetchChatAttachmentImpl(
  tokenProvider: WeakTokenProvider, srcUrl: string,
): Promise<Blob> {
  // APIのバージョンが変わっても呼べるようにする
  const prefixToRemove = /https:\/\/graph\.microsoft\.com\/(v[0-9]+\.[0-9]+|beta)\//;

  const client = initGraphClient(tokenProvider);
  if (!client) return Promise.reject(new Error(UseGraphApiError.TOKEN_PROVIDER_NOT_AVAILABLE));
  if (
    !srcUrl.startsWith('https://graph.microsoft.com/')
  ) return Promise.reject(new Error(UseGraphApiError.REQUIRED_PARAM_NOT_AVAILABLE));

  return client
    .api(srcUrl.replace(prefixToRemove, ''))
    .get()
    .then((res) => res);
}

function fetchChatTeamNameImpl(
  tokenProvider: WeakTokenProvider, teamId: string, chatId: string,
): Promise<IChatDetailBase> {
  const client = initGraphClient(tokenProvider);
  if (!client) return Promise.reject(new Error(UseGraphApiError.TOKEN_PROVIDER_NOT_AVAILABLE));

  return client
    .api(`teams/${teamId}/channels/${chatId}`)
    .get()
    .then((res) => res);
}

async function checkIsSelfChatRoom(client: Client, chatId: string): Promise<boolean> {
  const result = await client.api(`me/chats?$filter=id eq '${chatId}'`).get();
  return result['@odata.count'] === 0;
}

function fetchChatThreadTitleImpl(
  tokenProvider: WeakTokenProvider, teamId: string, chatId: string, messageId: string,

): Promise<IChannelDetail> {
  const client = initGraphClient(tokenProvider);
  if (!client) return Promise.reject(new Error(UseGraphApiError.TOKEN_PROVIDER_NOT_AVAILABLE));

  return client
    .api(`teams/${teamId}/channels/${chatId}/messages/${messageId}?$extends=members`)
    .get()
    .then((res) => res);
}

async function fetchChatMemberImpl(
  tokenProvider: WeakTokenProvider, chatId: string,

): Promise<Pick<IChatResponse & IChatDetail, 'topic' | 'members'>> {
  const client = initGraphClient(tokenProvider);
  if (!client) return Promise.reject(new Error(UseGraphApiError.TOKEN_PROVIDER_NOT_AVAILABLE));
  const isSelfChatRoom = await checkIsSelfChatRoom(client, chatId);
  if (isSelfChatRoom) {
    return { topic: '自分', members: [] };
  }
  return client
    .api(`me/chats/${chatId}?$expand=members`)
    .get()
    .then((res) => res);
}

export type ChatApiAccessorReturn = ReturnType<typeof useChatApiAccessor>;

const useChatApiAccessor = (
  tokenProvider: WeakTokenProvider,
  // cancellationRef: React.MutableRefObject<boolean>,
): UseChatApiReturnType => {
  /**
     * IDで指定したチャットの一覧をバッチリクエストで取得します。
     */

  const fetchChatRepliesInBackground = React.useCallback(
    (teamId, chatId, messageId, messageType) => fetchChatRepliesInBackgroundImpl(
      tokenProvider, teamId, chatId, messageId, messageType,
    ),
    [tokenProvider],
  );

  const fetchChatDetail = React.useCallback(
    (chatId, messageId) => fetchChatDetailImpl(
      tokenProvider, chatId, messageId,
    ),
    [tokenProvider],
  );

  /**
   * Teamsチャット添付ファイルデータの取得
   */
  const fetchChatAttachment: FetchChatAttachment = React.useCallback(
    async (srcUrl) => fetchChatAttachmentImpl(tokenProvider, srcUrl), [tokenProvider],
  );

  const fetchChatChannelName = React.useCallback(
    (teamId) => fetchChatChannelNameImpl(tokenProvider, teamId),
    [tokenProvider],
  );

  const fetchChatTeamName = React.useCallback(
    (teamId, chatId) => fetchChatTeamNameImpl(tokenProvider, teamId, chatId),
    [tokenProvider],
  );

  const fetchChatThreadTitle = React.useCallback(
    (teamId, chatId, messageId) => fetchChatThreadTitleImpl(
      tokenProvider, teamId, chatId, messageId,
    ),
    [tokenProvider],
  );

  const fetchChatMember = React.useCallback(
    (chatId) => fetchChatMemberImpl(tokenProvider, chatId),
    [tokenProvider],
  );

  /**
     * チャットにリアクションしたユーザーの表示名をバッチリクエストで取得する
     */
  const fetchReactionUserNames: FetchReactionUserNames = React.useCallback(
    (reactions) => fetchBulkReactionUserNames(
      tokenProvider, { current: false }, reactions,
    ), [tokenProvider],
  );

  return {
    fetchChatDetail: tokenProvider ? fetchChatDetail : undefined,
    fetchChatRepliesInBackground: tokenProvider ? fetchChatRepliesInBackground : undefined,
    fetchChatAttachment: tokenProvider ? fetchChatAttachment : undefined,
    fetchChatChannelName: tokenProvider ? fetchChatChannelName : undefined,
    fetchChatTeamName: tokenProvider ? fetchChatTeamName : undefined,
    fetchChatThreadTitle: tokenProvider ? fetchChatThreadTitle : undefined,
    fetchChatMember: tokenProvider ? fetchChatMember : undefined,
    fetchReactionUserNames: tokenProvider ? fetchReactionUserNames : undefined,
  };
};

export default useChatApiAccessor;
